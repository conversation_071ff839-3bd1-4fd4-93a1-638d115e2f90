import psycopg2
import random
import time
from faker import Faker
from datetime import date, timedelta

# --- 1. 数据库连接配置 (请修改为您的实际配置) ---
DB_CONFIG = {
    "dbname": "jd-management",  # 你的数据库名
    "user": "system",        # 你的用户名
    "password": "<PERSON>bon@888",    # 你的密码
    "host": "**************",            # 数据库主机地址
    "port": "54321"                  # 数据库端口
}

# --- 2. 测试参数配置 ---
INITIAL_INSERT_COUNT = 500      # 初始插入的数据量
OPERATIONS_COUNT = 1500         # 后续随机操作的总次数 (INSERT, UPDATE, DELETE)
TABLE_NAME = "sz_detox_personnel" # 目标表名

# 初始化 Faker，使用中文配置
fake = Faker('zh_CN')

def generate_random_record():
    """生成一条符合表结构的随机记录"""
    # 字典的 key 对应表中的列名
    record = {
        "jdrybm": fake.pystr_format(string_format='JDRYBM-##########'),
        "jdjgbm": str(random.randint(100000, 999999)),
        "bmbm": str(random.randint(1000, 9999)),
        "zp": fake.file_path(depth=3, category='image'),
        "xm": fake.name(),
        "xb": random.choice(['1', '2', '9']), # 1-男, 2-女, 9-未说明
        "csrq": fake.date_of_birth(minimum_age=18, maximum_age=65),
        "mz": random.choice(['01', '02', '03', '10']), # 假设 01-汉族, 02-蒙古族...
        "zjhm": fake.ssn(),
        "whcd": random.choice(['10', '20', '30', '40']), # 假设 10-小学, 20-初中...
        "hjszd": str(random.randint(100000, 999999)),
        "xjzdxxdz": fake.address(),
        "ydwxxlxdh": fake.phone_number(),
        "qzgljdksrq": fake.date_between(start_date='-5y', end_date='today'),
        "jdryzt": random.choice(['01', '02', '03', '04', '05', '06', '07', '08', '99']), # 戒毒人员状态
        "gldj": random.choice(['1', '2', '3']), # 管控等级
        "status": '1',
        "grjj": fake.text(max_nb_chars=200),
        # 其他字段可以根据需要添加，这里只填充了部分关键和非空字段
    }
    # 确保 qzgljdjsrq 在 qzgljdksrq 之后
    record["qzgljdjsrq"] = record["qzgljdksrq"] + timedelta(days=random.randint(90, 365*2))
    return record

def main():
    """主执行函数"""
    conn = None
    # 用于存储已插入记录的ID，方便后续的UPDATE和DELETE操作
    existing_ids = []

    try:
        # --- 3. 连接数据库 ---
        print("正在连接到 PostgreSQL 数据库...")
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        print("数据库连接成功！")

        # --- 4. 初始数据插入 ---
        print(f"\n--- 开始插入 {INITIAL_INSERT_COUNT} 条初始数据 ---")
        for i in range(INITIAL_INSERT_COUNT):
            record_data = generate_random_record()
            
            # 构建 INSERT 语句
            columns = record_data.keys()
            values = [record_data[col] for col in columns]
            sql = f"INSERT INTO {TABLE_NAME} ({', '.join(columns)}) VALUES ({', '.join(['%s'] * len(values))}) RETURNING id;"
            
            cur.execute(sql, values)
            new_id = cur.fetchone()[0]
            existing_ids.append(new_id)
            conn.commit()
            print(f"  [INSERT] 成功插入记录, ID: {new_id} ({i + 1}/{INITIAL_INSERT_COUNT})")
            time.sleep(0.05) # 轻微延迟，模拟真实业务场景

        print(f"--- 初始数据插入完成 ---")

        # --- 5. 随机执行 INSERT, UPDATE, DELETE ---
        print(f"\n--- 开始执行 {OPERATIONS_COUNT} 次随机操作 ---")
        for i in range(OPERATIONS_COUNT):
            if not existing_ids:
                # 如果所有记录都被删除了，就强制执行插入
                operation = 'INSERT'
            else:
                # 随机选择操作类型，增加 UPDATE 的权重
                operation = random.choice(['INSERT', 'UPDATE', 'UPDATE'])

            # --- 执行 UPDATE 操作 ---
            if operation == 'UPDATE' and existing_ids:
                record_id_to_update = random.choice(existing_ids)
                new_address = fake.address()
                new_phone = fake.phone_number()
                new_status = random.choice(['1', '0']) # 模拟人员状态变更
                
                sql_update = f"""
                    UPDATE {TABLE_NAME} 
                    SET 
                        xjzdxxdz = %s, 
                        ydwxxlxdh = %s, 
                        status = %s,
                        update_time = CURRENT_TIMESTAMP
                    WHERE id = %s;
                """
                cur.execute(sql_update, (new_address, new_phone, new_status, record_id_to_update))
                conn.commit()
                print(f"  [UPDATE] 更新了记录, ID: {record_id_to_update} ({i + 1}/{OPERATIONS_COUNT})")

            # --- 执行 DELETE 操作 ---
            elif operation == 'DELETE' and existing_ids:
                record_id_to_delete = random.choice(existing_ids)
                
                sql_delete = f"DELETE FROM {TABLE_NAME} WHERE id = %s;"
                cur.execute(sql_delete, (record_id_to_delete,))
                conn.commit()
                
                # 从列表中移除已删除的ID
                existing_ids.remove(record_id_to_delete)
                print(f"  [DELETE] 删除了记录, ID: {record_id_to_delete} ({i + 1}/{OPERATIONS_COUNT})")

            # --- 执行 INSERT 操作 ---
            else: # operation == 'INSERT'
                record_data = generate_random_record()
                columns = record_data.keys()
                values = [record_data[col] for col in columns]
                sql_insert = f"INSERT INTO {TABLE_NAME} ({', '.join(columns)}) VALUES ({', '.join(['%s'] * len(values))}) RETURNING id;"
                
                cur.execute(sql_insert, values)
                new_id = cur.fetchone()[0]
                existing_ids.append(new_id)
                conn.commit()
                print(f"  [INSERT] 新增了记录, ID: {new_id} ({i + 1}/{OPERATIONS_COUNT})")
            
            # 随机暂停一段时间，模拟真实世界的不均匀操作间隔
            time.sleep(random.uniform(0.05, 0.2))

        print("\n--- 所有测试操作执行完毕！ ---")

    except psycopg2.Error as e:
        print(f"数据库操作失败: {e}")
        if conn:
            conn.rollback() # 如果出错则回滚
    finally:
        if conn:
            cur.close()
            conn.close()
            print("数据库连接已关闭。")

if __name__ == '__main__':
    main()
