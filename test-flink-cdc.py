import psycopg2
import random
import time
from faker import Faker
from datetime import date, timedelta

# --- 1. 数据库连接配置 (请修改为您的实际配置) ---
DB_CONFIG = {
    "dbname": "jd-management",  # 你的数据库名
    "user": "system",        # 你的用户名
    "password": "<PERSON>bon@888",    # 你的密码
    "host": "**************",            # 数据库主机地址
    "port": "54321"                  # 数据库端口
}

# --- 2. 测试参数配置 ---
INITIAL_INSERT_COUNT = 500      # 初始插入的数据量
OPERATIONS_COUNT = 1500         # 后续随机操作的总次数 (INSERT, UPDATE, DELETE)
TABLE_NAME = "sz_detox_personnel" # 目标表名

# 初始化 Faker，使用中文配置
fake = Faker('zh_CN')

def generate_random_record():
    """生成一条符合表结构的随机记录"""
    # 字典的 key 对应表中的列名
    record = {
        "jdrybm": fake.pystr_format(string_format='JDRYBM-##########'),
        "jdjgbm": str(random.randint(100000, 999999)),
        "bmbm": str(random.randint(1000, 9999)),
        "zp": fake.file_path(depth=3, category='image'),
        "xm": fake.name(),
        "xb": random.choice(['1', '2', '9']), # 1-男, 2-女, 9-未说明
        "csrq": fake.date_of_birth(minimum_age=18, maximum_age=65),
        "mz": random.choice(['01', '02', '03', '10']), # 假设 01-汉族, 02-蒙古族...
        "zjhm": fake.ssn(),
        "whcd": random.choice(['10', '20', '30', '40']), # 假设 10-小学, 20-初中...
        "hjszd": str(random.randint(100000, 999999)),
        "xjzdxxdz": fake.address(),
        "ydwxxlxdh": fake.phone_number(),
        "qzgljdksrq": fake.date_between(start_date='-5y', end_date='today'),
        "jdryzt": random.choice(['01', '02', '03', '04', '05', '06', '07', '08', '99']), # 戒毒人员状态
        "gldj": random.choice(['1', '2', '3']), # 管控等级
        "status": '1',
        "grjj": fake.text(max_nb_chars=200),
        # 其他字段可以根据需要添加，这里只填充了部分关键和非空字段
    }
    # 确保 qzgljdjsrq 在 qzgljdksrq 之后
    record["qzgljdjsrq"] = record["qzgljdksrq"] + timedelta(days=random.randint(90, 365*2))
    return record

def main():
    """主执行函数"""
    conn = None
    # 用于存储已插入记录的ID，方便后续的UPDATE和DELETE操作
    existing_ids = []

    try:
        # --- 3. 连接数据库 ---
        print("正在连接到 PostgreSQL 数据库...")
        conn = psycopg2.connect(**DB_CONFIG)
        cur = conn.cursor()
        print("数据库连接成功！")

        # --- 4. 初始数据插入 ---
        print(f"\n--- 开始插入 {INITIAL_INSERT_COUNT} 条初始数据 ---")
        for i in range(INITIAL_INSERT_COUNT):
            record_data = generate_random_record()
            
            # 构建 INSERT 语句
            columns = record_data.keys()
            values = [record_data[col] for col in columns]
            sql = f"INSERT INTO {TABLE_NAME} ({', '.join(columns)}) VALUES ({', '.join(['%s'] * len(values))}) RETURNING id;"
            
            cur.execute(sql, values)
            new_id = cur.fetchone()[0]
            existing_ids.append(new_id)
            conn.commit()
            print(f"  [INSERT] 成功插入记录, ID: {new_id} ({i + 1}/{INITIAL_INSERT_COUNT})")
            time.sleep(0.05) # 轻微延迟，模拟真实业务场景

        print(f"--- 初始数据插入完成 ---")

        # --- 5. 随机执行 INSERT, UPDATE, DELETE ---
        print(f"\n--- 开始执行 {OPERATIONS_COUNT} 次随机操作 ---")
        for i in range(OPERATIONS_COUNT):
            if not existing_ids:
                # 如果所有记录都被删除了，就强制执行插入
                operation = 'INSERT'
            else:
                # 随机选择操作类型，增加 UPDATE 的权重
                operation = random.choice(['INSERT', 'UPDATE', 'UPDATE'])

            # --- 执行 UPDATE 操作 ---
            if operation == 'UPDATE' and existing_ids:
                record_id_to_update = random.choice(existing_ids)
                new_address = fake.address()
                new_phone = fake.phone_number()
                new_status = random.choice(['1', '0']) # 模拟人员状态变更
                
                sql_update = f"""
                    UPDATE {TABLE_NAME} 
                    SET 
                        xjzdxxdz = %s, 
                        ydwxxlxdh = %s, 
                        status = %s,
                        update_time = CURRENT_TIMESTAMP
                    WHERE id = %s;
                """
                cur.execute(sql_update, (new_address, new_phone, new_status, record_id_to_update))
                conn.commit()
                print(f"  [UPDATE] 更新了记录, ID: {record_id_to_update} ({i + 1}/{OPERATIONS_COUNT})")

            # --- 执行 DELETE 操作 ---
            elif operation == 'DELETE' and existing_ids:
                record_id_to_delete = random.choice(existing_ids)
                
                sql_delete = f"DELETE FROM {TABLE_NAME} WHERE id = %s;"
                cur.execute(sql_delete, (record_id_to_delete,))
                conn.commit()
                
                # 从列表中移除已删除的ID
                existing_ids.remove(record_id_to_delete)
                print(f"  [DELETE] 删除了记录, ID: {record_id_to_delete} ({i + 1}/{OPERATIONS_COUNT})")

            # --- 执行 INSERT 操作 ---
            else: # operation == 'INSERT'
                record_data = generate_random_record()
                columns = record_data.keys()
                values = [record_data[col] for col in columns]
                sql_insert = f"INSERT INTO {TABLE_NAME} ({', '.join(columns)}) VALUES ({', '.join(['%s'] * len(values))}) RETURNING id;"
                
                cur.execute(sql_insert, values)
                new_id = cur.fetchone()[0]
                existing_ids.append(new_id)
                conn.commit()
                print(f"  [INSERT] 新增了记录, ID: {new_id} ({i + 1}/{OPERATIONS_COUNT})")
            
            # 随机暂停一段时间，模拟真实世界的不均匀操作间隔
            time.sleep(random.uniform(0.05, 0.2))

        print("\n--- 所有测试操作执行完毕！ ---")

    except psycopg2.Error as e:
        print(f"数据库操作失败: {e}")
        if conn:
            conn.rollback() # 如果出错则回滚
    finally:
        if conn:
            cur.close()
            conn.close()
            print("数据库连接已关闭。")

def compare_tables():
    """比较两个数据库中的 sz_detox_personnel 表结构和数据"""

    # jd-management 数据库配置
    jd_management_config = {
        "dbname": "jd-management",
        "user": "system",
        "password": "Gobon@888",
        "host": "**************",
        "port": "54321"
    }

    # jdj 数据库配置 (需要根据实际情况修改)
    jdj_config = {
        "dbname": "jdj",
        "user": "system",  # 请修改为实际用户名
        "password": "Gobon@888",  # 请修改为实际密码
        "host": "**************",  # 请修改为实际主机地址
        "port": "54321"  # 请修改为实际端口
    }

    try:
        # 连接两个数据库
        print("正在连接到 jd-management 数据库...")
        conn1 = psycopg2.connect(**jd_management_config)
        cur1 = conn1.cursor()

        print("正在连接到 jdj 数据库...")
        conn2 = psycopg2.connect(**jdj_config)
        cur2 = conn2.cursor()

        print("数据库连接成功！\n")

        # 1. 比较表结构
        print("=== 比较表结构 ===")

        # 获取 jd-management 数据库中的表结构
        cur1.execute("""
            SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'sz_detox_personnel'
            ORDER BY ordinal_position;
        """)
        jd_management_columns = cur1.fetchall()

        # 获取 jdj 数据库中的表结构
        cur2.execute("""
            SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
            FROM information_schema.columns
            WHERE table_name = 'sz_detox_personnel'
            ORDER BY ordinal_position;
        """)
        jdj_columns = cur2.fetchall()

        print("jd-management 数据库中的表结构:")
        print("字段名\t\t数据类型\t\t可空\t\t默认值\t\t最大长度")
        print("-" * 80)
        for col in jd_management_columns:
            print(f"{col[0]:<20}\t{col[1]:<15}\t{col[2]:<10}\t{str(col[3]):<15}\t{str(col[4])}")

        print(f"\njdj 数据库中的表结构:")
        print("字段名\t\t数据类型\t\t可空\t\t默认值\t\t最大长度")
        print("-" * 80)
        for col in jdj_columns:
            print(f"{col[0]:<20}\t{col[1]:<15}\t{col[2]:<10}\t{str(col[3]):<15}\t{str(col[4])}")

        # 2. 比较字段差异
        print("\n=== 字段差异分析 ===")
        jd_management_fields = {col[0]: col[1:] for col in jd_management_columns}
        jdj_fields = {col[0]: col[1:] for col in jdj_columns}

        # 找出只在 jd-management 中存在的字段
        only_in_jd_management = set(jd_management_fields.keys()) - set(jdj_fields.keys())
        if only_in_jd_management:
            print(f"只在 jd-management 中存在的字段: {only_in_jd_management}")

        # 找出只在 jdj 中存在的字段
        only_in_jdj = set(jdj_fields.keys()) - set(jd_management_fields.keys())
        if only_in_jdj:
            print(f"只在 jdj 中存在的字段: {only_in_jdj}")

        # 找出两个数据库都有但类型不同的字段
        common_fields = set(jd_management_fields.keys()) & set(jdj_fields.keys())
        different_types = []
        for field in common_fields:
            if jd_management_fields[field] != jdj_fields[field]:
                different_types.append(field)
                print(f"字段 '{field}' 类型不同:")
                print(f"  jd-management: {jd_management_fields[field]}")
                print(f"  jdj: {jdj_fields[field]}")

        if not only_in_jd_management and not only_in_jdj and not different_types:
            print("✓ 表结构完全一致")

        # 3. 比较数据量
        print("\n=== 数据量比较 ===")
        cur1.execute(f"SELECT COUNT(*) FROM {TABLE_NAME};")
        jd_management_count = cur1.fetchone()[0]

        cur2.execute(f"SELECT COUNT(*) FROM {TABLE_NAME};")
        jdj_count = cur2.fetchone()[0]

        print(f"jd-management 数据库记录数: {jd_management_count}")
        print(f"jdj 数据库记录数: {jdj_count}")

        # 4. 数据一致性检查（抽样比较）
        print("\n=== 数据一致性检查 ===")
        if jd_management_count > 0 and jdj_count > 0:
            # 获取共同的字段进行比较
            common_field_list = list(common_fields)
            if common_field_list:
                # 随机抽取一些记录进行比较
                sample_size = min(10, jd_management_count, jdj_count)

                # 从 jd-management 获取样本数据
                cur1.execute(f"""
                    SELECT {', '.join(common_field_list)}
                    FROM {TABLE_NAME}
                    ORDER BY RANDOM()
                    LIMIT {sample_size};
                """)
                jd_management_sample = cur1.fetchall()

                # 从 jdj 获取样本数据
                cur2.execute(f"""
                    SELECT {', '.join(common_field_list)}
                    FROM {TABLE_NAME}
                    ORDER BY RANDOM()
                    LIMIT {sample_size};
                """)
                jdj_sample = cur2.fetchall()

                print(f"抽样比较 {sample_size} 条记录的数据格式...")
                print("注意: 这里只是格式比较，不是具体数据值比较")

                # 比较数据格式是否一致
                format_consistent = True
                for i in range(min(len(jd_management_sample), len(jdj_sample))):
                    jd_row = jd_management_sample[i]
                    jdj_row = jdj_sample[i]

                    for j, field in enumerate(common_field_list):
                        jd_val = jd_row[j]
                        jdj_val = jdj_row[j]

                        # 检查数据类型是否一致
                        if type(jd_val) != type(jdj_val):
                            print(f"记录 {i+1}, 字段 '{field}': 数据类型不一致")
                            print(f"  jd-management: {type(jd_val)} = {jd_val}")
                            print(f"  jdj: {type(jdj_val)} = {jdj_val}")
                            format_consistent = False

                if format_consistent:
                    print("✓ 抽样数据格式一致")

        print("\n=== 比较完成 ===")

    except psycopg2.Error as e:
        print(f"数据库操作失败: {e}")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 关闭连接
        try:
            if 'cur1' in locals():
                cur1.close()
            if 'conn1' in locals():
                conn1.close()
            if 'cur2' in locals():
                cur2.close()
            if 'conn2' in locals():
                conn2.close()
            print("数据库连接已关闭。")
        except:
            pass

if __name__ == '__main__':
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'compare':
        compare_tables()
    else:
        main()
